#!/usr/bin/env python3
"""
Bark TTS Installation Script for CoHost.AI.

This script installs the Suno Bark TTS package and its dependencies.
Run this after installing the base requirements.

Author: <PERSON>
License: MIT
"""

import subprocess
import sys
import os

def install_bark():
    """Install Bark TTS package."""
    print("🎵 Installing Suno Bark TTS...")
    print("=" * 50)
    
    try:
        # Install bark package
        print("📦 Installing bark package...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "git+https://github.com/suno-ai/bark.git"
        ])
        
        print("✅ Bark TTS installed successfully!")
        print()
        print("📋 Next steps:")
        print("1. Run 'python test_tts.py' to test TTS functionality")
        print("2. The first run will download models (~1GB) - this may take a few minutes")
        print("3. Configure your voice preset in .env file (BARK_VOICE_PRESET)")
        print()
        print("🎭 Available voice presets:")
        print("• v2/en_speaker_0 through v2/en_speaker_9 (English speakers)")
        print("• See https://github.com/suno-ai/bark for more options")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Bark TTS: {e}")
        print()
        print("💡 Troubleshooting:")
        print("• Ensure you have Python 3.9+ (Bark requires Python >= 3.9, < 3.12)")
        print("• Try upgrading pip: python -m pip install --upgrade pip")
        print("• Check your internet connection")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def check_python_version():
    """Check if Python version is compatible with Bark."""
    version = sys.version_info
    if version.major == 3 and 9 <= version.minor < 12:
        print(f"✅ Python {version.major}.{version.minor} is compatible with Bark TTS")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor} is not compatible with Bark TTS")
        print("   Bark requires Python >= 3.9 and < 3.12")
        return False

if __name__ == "__main__":
    print("🤖 CoHost.AI - Bark TTS Installation")
    print("=" * 50)
    
    if not check_python_version():
        sys.exit(1)
    
    if install_bark():
        print("🎉 Installation completed successfully!")
        sys.exit(0)
    else:
        print("❌ Installation failed. Please check the errors above.")
        sys.exit(1)
