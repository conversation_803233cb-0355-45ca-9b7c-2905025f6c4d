#!/usr/bin/env python3
"""
TTS Test Script for CoHost.AI.

This script provides independent testing of the text-to-speech functionality
to help debug audio issues and validate TTS configuration.

Author: Tom <PERSON>tz
License: MIT

Usage:
    python test_tts.py [voice_preset]

Requirements:
    - Bark TTS models (will be downloaded on first run)
    - Audio device properly configured
    - All dependencies installed
"""

import sys
import logging
from pathlib import Path

# Add the parent directory to the path to import src modules
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import with proper module path
try:
    from src.tts_manager import TTSManager
except ImportError as e:
    print(f"ERROR: Failed to import TTSManager: {e}")
    print("Make sure you're running this from the project root directory.")
    print(f"Current working directory: {Path.cwd()}")
    print(f"Project root: {project_root}")
    sys.exit(1)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_tts(voice_preset: str = "v2/en_speaker_6") -> bool:
    """
    Test TTS functionality independently.

    Performs a comprehensive test of the text-to-speech system including:
    - Bark TTS model initialization
    - Audio device validation
    - Speech synthesis
    - Audio playback

    Args:
        voice_preset: Bark voice preset to use for testing

    Returns:
        bool: True if all tests pass, False otherwise
    """
    # Configuration
    test_device_index = 7  # Recommended device from audio test
    test_text = "Hello! This is a test of the Bark text-to-speech system. Can you hear me?"

    print(f"🎵 Using voice preset: {voice_preset}")

    tts_manager = None
    try:
        print("🔧 Initializing TTS Manager...")
        tts_manager = TTSManager(
            voice_preset=voice_preset,
            device_index=test_device_index,
            cache_enabled=False  # Disable cache for clean testing
        )
        print("✅ TTS Manager initialized successfully")

        print("🎵 Testing TTS synthesis and playback...")
        print(f"   Text: '{test_text}'")
        print(f"   Voice: {voice_preset}")
        print(f"   Device: {test_device_index}")

        tts_manager.synthesize_and_play(test_text)

        print("✅ TTS test completed successfully!")
        print("   If you heard the audio, Bark TTS is working correctly.")
        return True

    except Exception as e:
        print(f"❌ ERROR: TTS test failed: {e}")
        print("\n📋 Debug Information:")
        import traceback
        traceback.print_exc()
        return False

    finally:
        if tts_manager:
            try:
                tts_manager.cleanup()
                print("🧹 TTS Manager cleaned up")
            except Exception as cleanup_error:
                print(f"⚠️  Warning: Cleanup failed: {cleanup_error}")

if __name__ == "__main__":
    print("=" * 60)
    print("BARK TTS TEST SCRIPT")
    print("=" * 60)

    # Get voice preset from command line argument
    voice_preset = sys.argv[1] if len(sys.argv) > 1 else "v2/en_speaker_6"

    success = test_tts(voice_preset)

    print("=" * 60)
    if success:
        print("✓ Bark TTS test PASSED")
    else:
        print("✗ Bark TTS test FAILED")
    print("=" * 60)
